{"name": "school", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --port 1440", "build": "vite build", "preview": "vite preview --port 1440", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test", "test:unit:watch": "vitest watch", "test:all": "npm run qdrant:start && npm run test && npm run qdrant:stop", "qdrant:start": "docker-compose up -d qdrant", "qdrant:stop": "docker-compose down", "qdrant:logs": "docker-compose logs -f qdrant"}, "dependencies": {"@auth/sveltekit": "^1.9.2", "@qdrant/js-client-rest": "^1.14.1", "@types/animejs": "^3.1.13", "@types/uuid": "^10.0.0", "animejs": "^4.0.2", "axios": "^1.9.0", "firebase": "^11.9.1", "flowbite": "^3.1.2", "qrcode": "^1.5.4", "tailwind-variants": "^1.0.0", "tw-animate-css": "^1.3.4", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@playwright/test": "^1.49.1", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.4", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "jsdom": "^26.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vitest": "^3.2.3"}}
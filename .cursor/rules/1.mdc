# School Management Dashboard - Rules

## Multitenancy & Database
- Qdrant vector database used for all data storage
- Single collection `i` stores all data points
- Multitenancy implemented via `s` field in payloads
  - Example: `s: 'sch'` for school records
- UUID v7 used for all unique identifiers

## Data Loading Patterns
For pages requiring initial data load from DB:

### Server Load Function (+page.server.ts)
```typescript
import { getById } from '$lib/db';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
  return {
    item: await getById(params.id)
  };
};
```

### Page Component (+page.svelte)
```svelte
<script lang="ts">
  import type { PageData } from './$types';
  export let data: PageData;
</script>

<h1 class="text-2xl font-bold">{data.item.title}</h1>
<div>{@html data.item.content}</div>
```

## Form Actions
Client-server data submission:

### Define Actions (+page.server.ts)
```typescript
import type { Actions } from './$types';

export const actions = {
  login: async ({ request }) => {
    const formData = await request.formData();
    // Authentication logic
  },
  register: async ({ request }) => {
    const formData = await request.formData();
    // Registration logic
  }
} satisfies Actions;
```

### Invoke Actions in UI
```svelte
<form method="POST" action="?/login">
  <!-- Login form fields -->
</form>

<form method="POST" action="/auth?/register">
  <!-- Registration form fields -->
</form>
```

## Authentication Flow
2. OAuth: `/auth/callback/google` (Google auth endpoint)
3. Session: Managed via `src/lib/stores/auth.ts`


## UI Components & Styling
- Always use best practices and recommended practices for site architecture according to the SvelteKit docs
- Core UI patterns:
  - Solid buttons w/ no border radius
  - Card components w/ border radius
  - Consistent spacing system (4px base)
  - Dark/light mode support

### Microinteractions
- Always create styles like an experienced creative designer at Apple
- Use `animejs` for all UI transitions
- Implement subtle animations for:
  - State changes (hover, active)
  - Data loading sequences
  - Route transitions

## TypeScript Conventions
- All entities defined in `src/lib/types.ts`
- Use SvelteKit generated types (`./$types`)
- Strict null checks enabled

## Testing Practices
1. Unit tests: Vitest (`.test.ts` files)
2. E2E tests: Playwright (`e2e/` directory)
3. Test coverage: Minimum 80% for core logic

## File Structure Conventions
```
src/
├── lib/           # Reusable modules
│   ├── db.ts      # Database operations
│   ├── auth.ts    # Auth utilities
│   └── stores/    # Svelte stores
├── routes/
│   ├── auth/      # Authentication routes
│   ├── s/         # School management
│   └── sc/        # Student classes
static/            # Static assets
```

## Runtime
- Start dev server: `deno task dev`
- Production build: `deno task build`

import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
  // COMMENTED OUT: Auth.js user check
  // if (locals.user) {
  //   throw redirect(303, '/d/');
  // }

  // PLACEHOLDER: Mock user check for development
  // TODO: Replace with custom authentication system
  if (locals.user) {
    console.log('PLACEHOLDER: User found, would redirect to /d/');
    // TODO: Implement proper authentication redirect
    // throw redirect(303, '/d/');
  }

  return {};
};
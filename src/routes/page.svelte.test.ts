import { render } from '@testing-library/svelte';
import { describe, it, expect, beforeAll, vi } from 'vitest';

describe('/+page.svelte', () => {
	it('should render login page', async () => {
		// Mock the page store before importing the component
		const { readable } = await import('svelte/store');
		await vi.doMock('$app/stores', () => ({
			page: readable({
				url: {
					searchParams: new URLSearchParams()
				}
			})
		}));

		// Import the component after mocking
		const Page = (await import('./+page.svelte')).default;
		
		const { container } = render(Page);
		const h1 = container.querySelector('h1');
		expect(h1).not.toBeNull();
		expect(h1?.textContent).toContain('School Portal');
		
		// Check for login button
		const loginButton = container.querySelector('button');
		expect(loginButton?.textContent).toContain('Continue with Google');
	});
});

<script lang="ts">
	import '../app.css';
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';

	let mounted = false;

	onMount(async () => {
		mounted = true;
		
		// Commented out anime.js animations
		/*
		const anime = ((await import('animejs')) as any).default || (await import('animejs'));
		
		const tl = anime.timeline({
			easing: 'easeInOutSine',
			loop: true,
			direction: 'alternate'
		});

		tl.add({
			targets: '.float-element',
			translateY: [-20, 20],
			duration: 4000,
			delay: anime.stagger(300)
		});
		*/
	});
</script>

<div class="min-h-screen gradient-bg relative overflow-hidden">
	<!-- Floating background elements -->
	<div class="absolute inset-0 overflow-hidden pointer-events-none">
		<div class="float-element absolute top-20 left-10 w-96 h-96 bg-blue-200/20 rounded-full blur-3xl"></div>
		<div class="float-element absolute bottom-20 right-20 w-80 h-80 bg-sky-300/20 rounded-full blur-3xl"></div>
		<div class="float-element absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-amber-200/20 rounded-full blur-3xl"></div>
	</div>
	

	
	<!-- Main content -->
	<main class="relative z-10 container mx-auto px-6 py-8">
		{#if mounted}
			<div in:fade={{ duration: 300 }}>
				<slot />
			</div>
		{/if}
	</main>
</div>

<style>
	:global(body) {
		overflow-x: hidden;
	}
</style>

import type { PageServerLoad } from './$types';
import { requireAuth } from '$lib/auth';
import { getById, searchByPayload } from '$lib/db';
import type { SchoolUser, School } from '$lib/db';

export const load: PageServerLoad = async (event) => {
	const session = await requireAuth(event.locals);

	// Get all school users for this user
	const searchPayload = {
		s: 'sch_usr',
		u: session.user?.id || ''
	};

	const schoolUsers = await searchByPayload<SchoolUser>(searchPayload);

	// Get all schools for these school users
	const schoolsPromises = schoolUsers.map(async (su) => {
		const school = await getById<School>(su.sc);
		return school;
	});

	const schools = await Promise.all(schoolsPromises);

	return {
		user: session.user,
		schools: schools.filter(Boolean)
	};
};

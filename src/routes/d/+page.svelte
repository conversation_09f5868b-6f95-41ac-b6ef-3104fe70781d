<script lang="ts">
	import { onMount } from 'svelte';
	import { scale } from 'svelte/transition';

	export let data;
	let animateCards = false;

	onMount(async () => {
		animateCards = true;
	});
</script>

<div class="mx-auto w-full max-w-6xl">
	<h1
		class="dashboard-title text-primary mb-12 text-center text-4xl font-bold md:text-5xl"
	>
		Dashboard
	</h1>

	<!-- Schools List -->
	{#if data.schools.length > 0}
		<div class="mb-8">
			<h2 class="mb-4 text-2xl font-semibold">Your Schools</h2>
			<div class="grid gap-8 md:grid-cols-2">
				{#each data.schools as school}
					{#if animateCards}
						<a
							href="/sc/{school.id}"
							class="card-glass group transition-all duration-300 hover:scale-105"
							in:scale={{ delay: 100, duration: 400 }}
						>
							<div class="flex flex-col space-y-4">
								<div class="flex items-center space-x-4">
									<div
										class="from-primary to-accent flex h-16 w-16 items-center justify-center rounded-xl bg-gradient-to-br text-2xl text-white"
									>
										🏫
									</div>
									<div>
										<h3 class="text-primary text-xl font-semibold">
											{school.n}
										</h3>
										<p class="text-sm text-gray-600">Click to manage</p>
									</div>
								</div>
							</div>
						</a>
					{/if}
				{/each}
			</div>
		</div>
	{/if}

	<!-- Actions -->
	<div class="grid gap-8 md:grid-cols-2">
		{#if animateCards}
			<!-- Create School Card -->
			<a
				href="/sc/a"
				class="card-glass group transition-all duration-300 hover:scale-105"
				in:scale={{ delay: 100, duration: 400 }}
			>
				<div class="flex flex-col items-center space-y-4 text-center">
					<div
						class="from-primary to-accent float-animation flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br text-3xl text-white"
					>
						🏫
					</div>
					<h2 class="text-primary text-2xl font-semibold">
						Create School
					</h2>
					<p class="text-gray-600">
						Set up a new school and manage academic records
					</p>
				</div>
			</a>

			<!-- Request Access Card -->
			<a
				href="/sc/ra"
				class="card-glass group transition-all duration-300 hover:scale-105"
				in:scale={{ delay: 200, duration: 400 }}
			>
				<div class="flex flex-col items-center space-y-4 text-center">
					<div
						class="from-secondary to-primary float-animation flex h-20 w-20 items-center justify-center rounded-2xl bg-gradient-to-br text-3xl text-white"
					>
						🎓
					</div>
					<h2 class="text-secondary text-2xl font-semibold">
						Request Access
					</h2>
					<p class="text-gray-600">
						Join an existing school as a student, teacher, or admin
					</p>
				</div>
			</a>
		{/if}
	</div>
</div>

<script lang="ts">
	import type { PageData } from './$types';
	export let data: PageData;
</script>

<div class="mx-auto max-w-xl py-12">
	<h1 class="mb-4 text-3xl font-bold">Student Details</h1>
	<div class="mb-6">
		<div class="text-xl font-semibold">{data.student.n}</div>
		<div class="text-gray-700">{data.student.cl}</div>
	</div>
	<a
		href={`/s/${data.student.id}/${data.student.c}/3`}
		class="inline-block rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
		>Go to Midterm Page</a
	>
</div>

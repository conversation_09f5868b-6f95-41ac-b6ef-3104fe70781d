<script lang="ts">
  import type { PageData } from './$types';
  export let data: PageData;
</script>

<div class="max-w-2xl mx-auto py-8">
  <h1 class="text-3xl font-bold mb-6">Students</h1>
  {#if data.students.length === 0}
    <p>No students found for this school.</p>
  {:else}
    <ul class="space-y-4">
      {#each data.students as student}
        <li class="p-4 border rounded flex justify-between items-center hover:bg-gray-50">
          <div>
            <div class="font-semibold text-lg">{student.n}</div>
            <div class="text-gray-600 text-sm">Class: {student.c}</div>
          </div>
          <a href={`/st/${student.id}`} class="text-blue-600 hover:underline">View Details</a>
        </li>
      {/each}
    </ul>
  {/if}
</div> 
<script lang="ts">
  import { enhance } from '$app/forms';
  import type { PageData, ActionData } from './$types';

  export let data: PageData;
  export let form: ActionData;
  
  let saving = false;
  let newYearName = '';

  $: classes = Object.entries(data.school.c)
    .filter(([key]) => key !== 'n')
    .sort(([a], [b]) => parseInt(a) - parseInt(b));
  
  function addYear() {
    if (!newYearName.trim()) return;
    
    const nextKey = data.school.c.n;
    data.school.c[nextKey] = newYearName.trim();
    data.school.c.n = nextKey + 1;
    newYearName = '';
  }
  
  function removeYear(key: string) {
    delete data.school.c[key];
  }
</script>

<div class="max-w-3xl mx-auto">
  <div class="edit-container card-glass">
    <h1 class="text-3xl font-bold text-primary mb-8 text-center">Edit School</h1>
    
    <form 
      method="POST"
      class="space-y-6" 
      use:enhance={() => {
        saving = true;
        return async ({ update }) => {
          await update();
          saving = false;
        };
      }}
    >
      <div>
        <label for="name" class="block text-sm font-medium mb-2">
          School Name
        </label>
        <input
          name="name"
          id="name"
          type="text"
          value={data.school.n}
          placeholder="Enter school name"
          class="input-neumorphic w-full"
          disabled={saving}
        />
      </div>
      
      <div>
        <label class="block text-sm font-medium mb-2">
          Classes
        </label>
        
        <div class="space-y-2 mb-4">
          {#each classes as [key, value]}
            <div class="flex items-center gap-2 p-3">
              <input 
                type="text"
                name="year_{key}"
                value={value}
                class="input-neumorphic flex-1"
                disabled={saving}
              />
              <button
                type="button"
                on:click={() => removeYear(key)}
                class="text-red-500 hover:text-red-700 transition-colors"
                disabled={saving}
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          {/each}
        </div>
        
        <div class="flex gap-2">
          <input
            type="text"
            bind:value={newYearName}
            placeholder="Add class"
            class="input-neumorphic flex-1"
            disabled={saving}
          />
          <button
            type="button"
            on:click={addYear}
            class="btn-neumorphic"
            disabled={saving || !newYearName.trim()}
          >
            Add
          </button>
        </div>
      </div>
      
      {#if form?.error}
        <div class="text-red-500 text-sm p-3">
          {form.error}
        </div>
      {/if}
      
      <div class="flex gap-4">
        <button
          type="submit"
          class="btn-neumorphic flex-1 disabled:opacity-50"
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
        
        <a
          href="/sc/{data.school.id}"
          class="btn-neumorphic text-center"
        >
          Cancel
        </a>
      </div>
    </form>
  </div>
</div>
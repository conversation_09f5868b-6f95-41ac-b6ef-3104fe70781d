import { fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { type School } from '$lib/db';
import { getById, upsertPoint } from '$lib/db';
import { requireAuth } from '$lib/auth';

export const load: PageServerLoad = async (event) => {
    await requireAuth(event.locals);
    const { params } = event;

    const schoolId = params.id;
    const school = await getById<School>(schoolId);

    if (!school) {
        return {
            status: 404,
            error: 'School not found'
        };
    }

    // Check if user is admin
    // const searchPayload: Record<string, any> = {
    //     s: 'sch_usr',
    //     u: session.user?.email || '',
    //     sc: schoolId
    // };
    
    // const schoolUsers = await searchByPayload<SchoolUser>(searchPayload);

    // if (!schoolUsers.length || !schoolUsers[0].r.includes('admin')) {
    //     return {
    //         status: 403,
    //         error: 'Unauthorized'
    //     };
    // }

    return {
        school
    };
};

export const actions = {
    default: async (event) => {
        await requireAuth(event.locals);
        const { params, request } = event;

        const schoolId = params.id;
        const school = await getById<School>(schoolId);

        if (!school) {
            return fail(404, { error: 'School not found' });
        }

        // Check if user is admin
        // const searchPayload: Record<string, any> = {
        //     s: 'sch_usr',
        //     u: session.user?.email || '',
        //     sc: schoolId
        // };

        // const schoolUsers = await searchByPayload<SchoolUser>(searchPayload);

        // if (!schoolUsers.length || !schoolUsers[0].r.includes('admin')) {
        //     return fail(403, { error: 'Unauthorized' });
        // }

        const formData = await request.formData();
        const name = formData.get('name')?.toString();
        const classes = Object.fromEntries(
            Array.from(formData.entries())
                .filter(([key]) => key.startsWith('year_'))
                .map(([key, value]) => [key.replace('year_', ''), value])
        );

        if (!name?.trim()) {
            return fail(400, { error: 'School name is required' });
        }

        try {
            // Update school
            await upsertPoint({
                ...school,
                n: name,
                c: {
                    ...classes,
                    n: Math.max(...Object.values(classes).map(v => parseInt(v.toString()) || 0)) + 1
                }
            });

            return { success: true };
        } catch (error) {
            console.error('Error updating school:', error);
            return fail(500, { error: 'Failed to update school' });
        }
    }
} satisfies Actions;

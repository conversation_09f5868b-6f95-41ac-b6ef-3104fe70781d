<script lang="ts">
  import { goto } from '$app/navigation';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Dialog, DialogContent, DialogHeader, DialogTitle } from '$lib/components/ui/dialog';
  import type { PageData } from './$types';
  import type { School, SchoolUser } from '$lib/types';
  
  export let data: PageData;
  
  let showStudentSearch = false;
  let searchQuery = '';
  let searchResults: SchoolUser[] = [];
  let searching = false;
  
  function selectStudent(student: SchoolUser) {
    showStudentSearch = false;
    goto(`/s/a?student=${student.id}`);
  }
</script>


{#if !data.school}
  <div class="flex justify-center items-center min-h-[50vh]">
    <p class="text-red-500">School not found</p>
  </div>
{:else}
  <div class="max-w-4xl mx-auto">
    <div class="detail-container">
      <!-- School Header -->
      <Card class="mb-8">
        <CardHeader>
          <div class="flex items-center justify-between mb-4">
            <CardTitle class="text-4xl font-bold">{data.school.n}</CardTitle>
            {#if data.isAdmin}
              <Button link={true} href={`/sc/${data.school.id}/edit`} variant="outline">
                Edit School
              </Button>
            {/if}
          </div>
        </CardHeader>
        <CardContent>
          <div class="grid md:grid-cols-2 gap-4 mt-6">
            <Card>
              <CardHeader>
                <h3 class="text-sm font-medium text-gray-600 mb-1">Academic Years</h3>
              </CardHeader>
              <CardContent>
                <p class="text-lg">
                  {Object.keys(data.school.c).filter(k => k !== 'n').length} configured
                </p>
              </CardContent>
            </Card>
            
            {#if data.schoolUser}
              <Card>
                <CardHeader>
                  <h3 class="text-sm font-medium text-gray-600 mb-1">Your Role</h3>
                </CardHeader>
                <CardContent>
                  <p class="text-lg">{data.schoolUser.r}</p>
                </CardContent>
              </Card>
            {/if}
          </div>
        </CardContent>
      </Card>
      
      <!-- Admin Actions -->
      {#if data.isAdmin}
        <Card>
          <CardHeader>
            <CardTitle class="text-2xl font-semibold">Admin Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="grid md:grid-cols-2 gap-4">
              <Button on:click={() => showStudentSearch = true} extraClass="flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Score
              </Button>
              
              <Button link={true} href={`/sc/${data.school.id}/as`} variant="outline" extraClass="flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Student
              </Button>
              
              <Button link={true} href={`/sc/${data.school.id}/s`} variant="outline" extraClass="flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                View Students
              </Button>
              
              <Button link={true} href="/sc/r" variant="outline" extraClass="flex items-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                View Join Requests
              </Button>
            </div>
          </CardContent>
        </Card>
      {/if}
    </div>
  </div>
  
  <!-- Student Search Modal -->
  <Dialog bind:open={showStudentSearch}>
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Search Student</DialogTitle>
      </DialogHeader>
      <form on:submit|preventDefault={async () => {
        if (!searchQuery.trim()) return;
        
        searching = true;
        try {
          // In a real implementation, this would call a server action
          // For now, we'll simulate search
          await new Promise(resolve => setTimeout(resolve, 500));
          searchResults = []; // Would be populated from server
        } finally {
          searching = false;
        }
      }} class="space-y-4">
        <div class="space-y-2">
          <Label for="search">Student Name</Label>
          <Input 
            id="search"
            name="search"
            type="text"
            bind:value={searchQuery}
            placeholder="Enter student name"
            disabled={searching}
            min={undefined}
            max={undefined}
          />
        </div>
        
        {#if searchResults.length > 0}
          <div class="space-y-2">
            {#each searchResults as student}
              <Button 
                variant="outline" 
                extraClass="w-full text-left justify-start" 
                on:click={() => selectStudent(student)}
              >
                <div class="flex flex-col">
                  <p class="font-medium">{student.n}</p>
                  <p class="text-sm text-gray-600">Role: {Array.isArray(student.r) ? student.r.join(', ') : student.r}</p>
                </div>
              </Button>
            {/each}
          </div>
        {/if}
        
        <div class="flex gap-2">
          <Button type="submit" extraClass="flex-1" disabled={searching || !searchQuery.trim()}>
            {searching ? 'Searching...' : 'Search'}
          </Button>
          <Button variant="outline" on:click={() => showStudentSearch = false}>
            Cancel
          </Button>
        </div>
      </form>
    </DialogContent>
  </Dialog>
{/if}

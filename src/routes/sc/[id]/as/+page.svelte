<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { enhance } from '$app/forms';
  import { get } from 'svelte/store';
  import type { PageProps } from './$types';
  import { Label } from '$lib/components/ui/label';
  import { Input } from '$lib/components/ui/input';
  import { Button } from '$lib/components/ui/button';
  import { Select } from '$lib/components/ui/select';

  let { data, form }: PageProps = $props();
</script>

<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-200 py-8">
  <div class="bg-white shadow-2xl p-8 w-full max-w-md border border-gray-100" style="border-radius:0;">
    <h1 class="text-2xl font-bold mb-6 text-gray-800 text-center tracking-tight">Add Student</h1>
    <form method="POST" use:enhance class="space-y-6">
      <Label class="block text-gray-700 text-sm font-medium mb-2">
        Student Name
        <Input name="name" id={undefined} min={undefined} max={undefined} />
      </Label>
      <Label class="block text-gray-700 text-sm font-medium mb-2">
        Student Class
        <Select name="studentClass" required id={undefined}>
          <option value="" disabled selected>Select class</option>
          {#each Object.entries(data.school.c) as c}
            <option value={c[0]}>{c[1]}</option>
          {/each}
        </Select>
      </Label>
      <!-- {#if form?.error}
        <div class="error">{form?.error}</div>
      {/if} -->
      <!-- {#if success}
        <div class="success">Student added!</div>
      {/if} -->
      <Button type="submit" variant="primary">Add Student</Button>
    </form>
  </div>
</div> 
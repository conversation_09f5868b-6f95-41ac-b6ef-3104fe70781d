import type { Actions } from '@sveltejs/kit';
import { error, redirect } from '@sveltejs/kit';
import type { School, SchoolUser } from '$lib/types';
import { getById, upsertPoint } from '$lib/db';

export const load = async ({ params }) => {
  const school = await getById<School>(params.id);
  if (!school) throw error(404, 'School not found');
  return { school };
};

export const actions: Actions = {
  default: async ({ request, params }) => {
    const data = await request.formData();
    const name = data.get('name') as string;
    const studentClass = data.get('studentClass') as string;
    if (!name || !studentClass) throw error(400, 'Missing fields');
    const schoolId = params.id;
    if (!schoolId) throw error(400, 'Missing school id');
    // Create new SchoolUser
    const profile: SchoolUser = {
      s: 'sch_usr',
      n: name,
      r: 'student',
      u: '',
      sc: schoolId,
      c: parseInt(studentClass)
    };
    const res = await upsertPoint(profile);
    redirect(303, '/st/' + res.id);
  }
};
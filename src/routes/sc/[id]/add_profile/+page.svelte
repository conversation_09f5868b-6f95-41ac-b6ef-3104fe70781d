<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { v7 as uuidv7 } from 'uuid';
  
  let school: School | null = null;
  let loading = true;
  let saving = false;
  let error = '';
  let userEmail = '';
  let name = '';
  let role = 'student';
  
  onMount(async () => {
    // Check authentication
    if (!$page.data.session) {
      goto('/');
      return;
    }
    
    await loadSchool();
  });
  
  async function loadSchool() {
    try {
      const schoolId = $page.params.id || '';
      school = await getById<School>(COLLECTIONS.SCHOOLS, schoolId);
      
      if (!school) {
        goto('/d/');
        return;
      }
      
      // Check if user has admin rights
      const schoolUsers = await searchByPayload(COLLECTIONS.SCHOOL_USERS, {
        s: 'sch_usr',
        u: $page.data.session?.user?.email || '',
        sc: schoolId
      });
      
      if (schoolUsers.length === 0 || !schoolUsers[0].r.includes('admin')) {
        goto(`/sc/${schoolId}`);
        return;
      }
    } catch (err) {
      console.error('Error loading school:', err);
      error = 'Failed to load school';
    } finally {
      loading = false;
    }
  }
  
  async function createProfile(e: Event) {
    e.preventDefault();
    if (!school) return;
    
    saving = true;
    error = '';
    
    try {
      // Create new profile
      const profile: SchoolUser = {
        id: uuidv7(),
        s: 'sch_usr',
        sc: school.id,
        r: [role],
        u: '', // User ID will be filled when they accept
        e: userEmail,
        n: name
      };
      
      await upsertPoint(COLLECTIONS.SCHOOL_USERS, profile);
      
      // Redirect back to school page
      goto(`/sc/${school.id}`);
    } catch (err) {
      console.error('Error creating profile:', err);
      error = 'Failed to create profile';
    } finally {
      saving = false;
    }
  }
</script>

{#if loading}
  <div class="flex justify-center items-center min-h-[50vh]">
    <div class="glassmorphic p-4">
      <div class="animate-spin h-8 w-8 border-b-2 border-primary"></div>
    </div>
  </div>
{:else if school}
  <div class="max-w-2xl mx-auto">
    <div class="card-glass" style="opacity: 0">
      <h1 class="text-3xl font-bold text-primary mb-8 text-center">Add Profile to {school.n}</h1>
      
      <form on:submit={createProfile} class="space-y-6">
        <!-- Email -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-400 mb-2">
            Email <span class="text-red-500">*</span>
          </label>
          <input
            id="email"
            type="email"
            bind:value={userEmail}
            placeholder="Enter email address"
            class="input-neumorphic"
            required
          />
        </div>
        
        <!-- Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-400 mb-2">
            Name <span class="text-red-500">*</span>
          </label>
          <input
            id="name"
            type="text"
            bind:value={name}
            placeholder="Enter full name"
            class="input-neumorphic"
            required
          />
        </div>
        
        <!-- Role -->
        <div>
          <label for="role" class="block text-sm font-medium text-gray-400 mb-2">
            Role <span class="text-red-500">*</span>
          </label>
          <select
            id="role"
            bind:value={role}
            class="input-neumorphic"
            required
          >
            <option value="student">Student</option>
            <option value="teacher">Teacher</option>
            <option value="admin">Administrator</option>
          </select>
        </div>
        
        {#if error}
          <div class="glassmorphic bg-red-900/30 text-red-200 text-sm">
            {error}
          </div>
        {/if}
        
        <div class="flex gap-4">
          <button
            type="submit"
            class="btn-neumorphic flex-1"
            disabled={saving}
          >
            {saving ? 'Creating...' : 'Create Profile'}
          </button>
          
          <a
            href="/sc/{school.id}"
            class="btn-neumorphic text-center"
          >
            Cancel
          </a>
        </div>
      </form>
    </div>
  </div>
{/if}

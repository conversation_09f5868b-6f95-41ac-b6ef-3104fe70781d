<script lang="ts">
  import { onMount } from 'svelte';
  import { enhance } from '$app/forms';
  let appUser = {};
  import type { PageServerData, ActionData } from './$types';
  
  export let data: PageServerData;
  export let form: ActionData;
  
  let useDifferentName = false;
  let customName = '';
  
  onMount(async () => {
    // Commented out animations
    /*
    const anime = ((await import('animejs')) as any).default || (await import('animejs'));
    
    // Animate form
    anime({
      targets: '.request-container',
      opacity: [0, 1],
      translateY: [30, 0],
      duration: 800,
      easing: 'easeOutCubic'
    });
    */
  });
</script>

{#if data.schools.length}
  <div class="max-w-2xl mx-auto">
    <div class="request-container card-glass" style="opacity: 0">
      <h1 class="text-3xl font-bold text-primary mb-8 text-center">Request School Access</h1>
      
      {#if form?.success}
        <div class="text-center py-12">
          <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-green-100 mb-4">
            <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="text-2xl font-semibold text-gray-800 mb-2">Request Submitted!</h2>
          <p class="text-gray-600">Your join request has been sent to the school administrators.</p>
        </div>
      {:else}
        <form method="POST" action="?/request" use:enhance class="space-y-6">
          <!-- School Selection -->
          <div>
            <label for="school" class="block text-sm font-medium text-gray-700 mb-2">
              Select School
            </label>
            <select
              id="school"
              name="schoolId"
              class="input-neumorphic w-full"
              required
            >
              <option value="">Choose a school</option>
              {#each data.schools as school}
                <option value={school.id}>{school.n}</option>
              {/each}
            </select>
          </div>
          
          <!-- Role Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Select Role
            </label>
            <div class="grid grid-cols-3 gap-3">
              {#each (['student', 'teacher', 'admin'] as const) as role}
                <label class="p-3 rounded-lg transition-all hover:cursor-pointer">
                  <input
                    type="radio"
                    name="role"
                    value={role}
                    required
                    class="hidden"
                  />
                  <div class="p-3 rounded-lg transition-all {form?.role === role ? 'neumorphic-inset' : 'glassmorphic'}">
                    <span class="text-sm font-medium capitalize">{role}</span>
                  </div>
                </label>
              {/each}
            </div>
          </div>
          
          <!-- Name Configuration -->
          <div class="glassmorphic p-4 rounded-lg">
            <p class="text-sm text-gray-700 mb-3">
              Your Google account name: <strong>{$appUser?.n}</strong>
            </p>
            <label class="flex items-center gap-2">
              <input
                type="checkbox"
                bind:checked={useDifferentName}              class="rounded border-gray-300"
              />
              <span class="text-sm">Use a different name</span>
            </label>
            
            {#if useDifferentName}
              <input
                type="text"
                name="customName"
                placeholder="Enter your preferred name"
                class="input-neumorphic w-full mt-3"
              />
            {/if}
          </div>
          
          {#if form?.error}
            <div class="text-red-500 text-sm glassmorphic p-3 rounded-lg">
              {form.error}
            </div>
          {/if}
          
          <div class="flex gap-4">
            <button
              type="submit"
              class="btn-neumorphic flex-1"
            >
              Submit Request
            </button>
            
            <a
              href="/d/"
              class="btn-neumorphic text-center"
            >
              Cancel
            </a>
          </div>
        </form>
      {/if}
    </div>
  </div>
{/if}
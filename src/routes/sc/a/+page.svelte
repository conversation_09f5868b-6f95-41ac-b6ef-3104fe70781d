<script lang="ts">
	import { onMount } from 'svelte';
	import { applyAction, enhance } from '$app/forms';
	import type { ActionData } from './$types';
	import { goto } from '$app/navigation';

	export let form: ActionData;
	

	onMount(async () => {
		// const anime =
		// 	((await import('animejs')) as any).default ||
		// 	(await import('animejs'));

		// // Animate form
		// anime({
		// 	targets: '.form-container',
		// 	opacity: [0, 1],
		// 	translateY: [30, 0],
		// 	duration: 800,
		// 	easing: 'easeOutCubic'
		// });
	});
</script>

<div class="mx-auto max-w-2xl">
	<div class="form-container card-glass">
		<h1 class="text-primary mb-8 text-center text-3xl font-bold">
			Create New School
		</h1>

		<form method="POST" use:enhance={() => {
			return ({ result }) => {
				if (result.type === 'success') {
					goto(`/sc/${result.data.id}/edit`);
				}
			}
		}} class="space-y-6">
			<div>
				<label
					for="name"
					class="mb-2 block text-sm font-medium text-gray-700"
				>
					School Name
				</label>
				<input
					id="name"
					name="name"
					type="text"
					placeholder="Enter school name"
					class="input-neumorphic w-full"
					required
				/>
			</div>

			{#if form?.error}
				<div class="glassmorphic rounded-lg p-3 text-sm text-red-500">
					{form.error}
				</div>
			{/if}

			<div class="flex gap-4">
				<button type="submit" class="btn-neumorphic flex-1">
					Create School
				</button>

				<a href="/d/" class="btn-neumorphic text-center"> Cancel </a>
			</div>
		</form>
	</div>
</div>

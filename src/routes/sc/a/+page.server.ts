import type { Actions, PageServerLoad } from './$types';
import { fail, redirect } from '@sveltejs/kit';
import {

	upsertPoint
} from '$lib/db';
import { requireAuth } from '$lib/auth';
import type { School, SchoolUser } from '$lib/types';

export const load: PageServerLoad = async (event) => {
	await requireAuth(event.locals);
	return {};
};

export const actions: Actions = {
	default: async (event) => {
		const session = await requireAuth(event.locals);
		const { request } = event;

		const data = await request.formData();
		const name = data.get('name')?.toString();

		if (!name) {
			return fail(400, { error: 'Name is required' });
		}

		try {
			// Create school if it doesn't exist
			const schoolData: School = {
				s: 'sch',
				n: name.trim(),
				c: {n: 0}
			};
			const savedSchool = await upsertPoint(schoolData);

			// Create school user record
			const schoolUser: SchoolUser = {
				s: 'sch_usr',
				r: 'admin',
				u: session.user?.id || '',
				sc: savedSchool.id
			};
			await upsertPoint(schoolUser);

			// redirect(303, `/sc/${savedSchool.id}/edit`);
			return { id: savedSchool.id };
			// return { redirect: `/sc/${savedSchool.id}/edit` };
		} catch (e) {
			console.error('Error creating school:', e);
			return fail(500, { error: 'Failed to create school' });
		}
	}
};

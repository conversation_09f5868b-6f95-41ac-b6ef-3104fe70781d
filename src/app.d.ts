import type { User } from '$lib/db';

// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
            user: User | null;
            session: {
                user?: {
                    name?: string;
                    email: string;
                };
            } | null;
            // PLACEHOLDER: Mock auth function
            auth: () => Promise<{
                user?: {
                    id?: string;
                    name?: string;
                    email?: string;
                    image?: string;
                };
            } | null>;
        }
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};

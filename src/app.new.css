@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  /* Brand colors */
  --color-primary: #2E338C;
  --color-accent: #04B2D9;
  --color-warning: #D9933D;
  --color-bg-soft: #E9ECEF;
}

/* Base theme */
body {
  background-color: #18181B;
  color: #F3F4F6;
}

.gradient-bg {
  background: linear-gradient(to bottom right, #1a1a1a, #2a2a2a);
}

.gradient-bg {
  background: linear-gradient(to bottom right, #1a1a1a, #2a2a2a);
}

/* Remove all borders, outlines, and border-radius */
*,
*::before,
*::after {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* Component styles without borders */
.card-glass {
  background-color: rgba(24, 24, 27, 0.5);
  backdrop-filter: blur(16px);
  padding: 1.5rem;
  position: relative;
}

.glassmorphic {
  background-color: rgba(39, 39, 42, 0.3);
  backdrop-filter: blur(8px);
  padding: 1rem;
  position: relative;
}

.btn-neumorphic {
  padding: 0.75rem 1.5rem;
  background-color: rgba(39, 39, 42, 0.8);
  color: rgba(255, 255, 255, 0.9);
  transition: all 300ms;
  position: relative;
}

.btn-neumorphic:hover {
  background-color: rgba(39, 39, 42, 1);
  color: rgba(255, 255, 255, 1);
}

.input-neumorphic {
  padding: 0.5rem 1rem;
  background-color: rgba(39, 39, 42, 0.5);
  color: white;
  width: 100%;
  transition: background-color 200ms;
  position: relative;
}

.input-neumorphic:focus {
  background-color: rgba(39, 39, 42, 0.7);
}

.input-neumorphic::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Hover effects using pseudo-elements */
.btn-neumorphic::after,
.input-neumorphic::after,
.card-glass::after,
.glassmorphic::after {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.03);
  opacity: 0;
  transition: opacity 200ms;
  pointer-events: none;
}

.btn-neumorphic:hover::after,
.input-neumorphic:focus::after {
  opacity: 1;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

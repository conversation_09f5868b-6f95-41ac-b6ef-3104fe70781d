<script lang="ts">
  export let type = 'text';
  export let value: string | number = '';
  export let placeholder = '';
  export let disabled = false;
  export let id: string | undefined;
  export let name: string | undefined;
  export let min: number | undefined;
  export let max: number | undefined;
</script>

<input
  class="input-neumorphic"
  {type}
  {placeholder}
  {disabled}
  {id}
  {name}
  {min}
  {max}
  bind:value
/>

<style>
.input-neumorphic {
  background: #f3f4f6;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  outline: none;
  box-shadow: 2px 2px 8px #e5e7eb, -2px -2px 8px #fff;
  transition: border 0.2s;
}
.input-neumorphic:focus {
  border: 1.5px solid var(--color-primary, #2563eb);
}
</style> 
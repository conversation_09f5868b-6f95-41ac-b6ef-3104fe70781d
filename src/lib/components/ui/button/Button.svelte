<script lang="ts">
  export let variant: 'primary' | 'outline' | 'default' = 'default';
  export let type: 'button' | 'submit' | 'reset' = 'button';
  export let disabled = false;
  export let extraClass: string = '';
  export let link: boolean = false;
  export let href: string = '';
  export let ariaLabel: string | undefined = undefined;
</script>

{#if link}
  <a
    class={`btn ${variant} ${extraClass}`}
    href={href}
    aria-label={ariaLabel}
  >
    <slot />
  </a>
{:else}
  <button
    class={`btn ${variant} ${extraClass}`}
    type={type}
    disabled={disabled}
    aria-label={ariaLabel}
    on:click
  >
    <slot />
  </button>
{/if}

<style>
.btn {
  padding: 0.5rem 1.25rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: background 0.2s, color 0.2s, border 0.2s;
  border: none;
  cursor: pointer;
  background: #f3f4f6;
  color: #222;
}
.btn.primary {
  background: var(--color-primary, #2563eb);
  color: #fff;
}
.btn.outline {
  background: transparent;
  border: 1.5px solid var(--color-primary, #2563eb);
  color: var(--color-primary, #2563eb);
}
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style> 
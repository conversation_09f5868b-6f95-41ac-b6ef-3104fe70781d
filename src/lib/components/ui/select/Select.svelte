<script lang="ts">
  export let name: string | undefined;
  export let required: boolean = false;
  export let disabled: boolean = false;
  export let id: string | undefined;
</script>

<select
  class="select-sleek"
  {name}
  {required}
  {disabled}
  {id}
>
  <slot />
</select>

<style>
.select-sleek {
  background: #f3f4f6;
  border: 1.5px solid #e5e7eb;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  outline: none;
  box-shadow: 2px 2px 8px #e5e7eb, -2px -2px 8px #fff;
  transition: border 0.2s;
  border-radius: 0;
}
.select-sleek:focus {
  border: 1.5px solid var(--color-primary, #2563eb);
}
</style> 
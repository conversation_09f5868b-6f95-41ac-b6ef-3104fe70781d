<script lang="ts">
  export let open = false;
  // Svelte's bind:open will work automatically for two-way binding
</script>

{#if open}
  <div class="dialog-overlay">
    <div class="dialog-modal">
      <slot />
    </div>
  </div>
{/if}

<style>
.dialog-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.dialog-modal {
  background: #fff;
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.18);
  padding: 2rem;
  min-width: 320px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}
</style> 
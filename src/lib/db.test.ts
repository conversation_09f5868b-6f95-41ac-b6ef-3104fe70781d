import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import {
	qdrantClient,
	COLLECTIONS,
	initializeCollections,
	upsertPoint,
	searchByPayload,
	searchByName,
	getById,
	deleteById,
	updatePoint,
	generateId,
	type User,
	type School,
	type SchoolUser,
	type Score,
	type AccessRequest
} from './db';

describe('Database Operations', () => {
	beforeAll(async () => {
		// Initialize collections before tests
		await initializeCollections();
	});

	afterAll(async () => {
		// Clean up test data
		const collections = Object.values(COLLECTIONS);
		for (const collection of collections) {
			try {
				await qdrantClient.deleteCollection(collection);
			} catch (err) {
				// Collection might not exist
			}
		}
	});

	describe('generateId', () => {
		it('should generate valid UUID v7', () => {
			const id = generateId();
			expect(id).toMatch(
				/^[0-9a-f]{8}-[0-9a-f]{4}-7[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
			);
		});

		it('should generate unique IDs', () => {
			const id1 = generateId();
			const id2 = generateId();
			expect(id1).not.toBe(id2);
		});
	});

	describe('User operations', () => {
		it('should create and retrieve a user', async () => {
			const user: User = {
				s: 'u',
				n: 'Test User',
				e: '<EMAIL>',
				g: 'google_123'
			};

			const savedUser = await upsertPoint(COLLECTIONS.USERS, user);
			expect(savedUser.id).toBeDefined();
			expect(savedUser.n).toBe(user.n);

			const retrieved = await getById<User>(
				COLLECTIONS.USERS,
				savedUser.id!
			);
			expect(retrieved).toEqual(savedUser);
		});

		it('should search users by payload', async () => {
			const user: User = {
				s: 'u',
				n: 'Search Test User',
				e: '<EMAIL>',
				g: 'google_search'
			};

			await upsertPoint(COLLECTIONS.USERS, user);

			const results = await searchByPayload<User>(COLLECTIONS.USERS, {
				s: 'u',
				g: 'google_search'
			});

			expect(results.length).toBeGreaterThan(0);
			expect(results[0].g).toBe('google_search');
		});
	});

	describe('School operations', () => {
		it('should create and retrieve a school', async () => {
			const school: School = {
				s: 'sch',
				n: 'Test School',
				c: { next: 1 }
			};

			const savedSchool = await upsertPoint(
				COLLECTIONS.SCHOOLS,
				school
			);
			expect(savedSchool.id).toBeDefined();
			expect(savedSchool.n).toBe(school.n);
			expect(savedSchool.c.next).toBe(1);
		});

		it('should update school academic years', async () => {
			const school: School = {
				s: 'sch',
				n: 'Update Test School',
				c: { next: 1 }
			};

			const savedSchool = await upsertPoint(
				COLLECTIONS.SCHOOLS,
				school
			);

			// Add academic years
			const updatedData = {
				c: {
					'1': '2023/2024',
					'2': '2024/2025',
					next: 3
				}
			};

			await updatePoint(
				COLLECTIONS.SCHOOLS,
				savedSchool.id!,
				updatedData
			);

			const updated = await getById<School>(
				COLLECTIONS.SCHOOLS,
				savedSchool.id!
			);
			expect(updated?.c['1']).toBe('2023/2024');
			expect(updated?.c['2']).toBe('2024/2025');
			expect(updated?.c.next).toBe(3);
		});
	});

	describe('SchoolUser operations', () => {
		it('should create school user with roles', async () => {
			const userId = generateId();
			const schoolId = generateId();

			const schoolUser: SchoolUser = {
				s: 'sch_usr',
				r: 'admin',
				n: 'Admin User',
				u: userId,
				sc: schoolId
			};

			const saved = await upsertPoint(
				COLLECTIONS.SCHOOL_USERS,
				schoolUser
			);
			expect(saved.r).toContain('admin');
		});

		it('should search school users by name', async () => {
			const schoolUser: SchoolUser = {
				s: 'sch_usr',
				r: 'student',
				n: 'John Doe Student',
				u: generateId(),
				sc: generateId()
			};

			await upsertPoint(COLLECTIONS.SCHOOL_USERS, schoolUser);

			const results = await searchByName<SchoolUser>(
				COLLECTIONS.SCHOOL_USERS,
				'John',
				'sch_usr',
				3
			);

			expect(results.length).toBeGreaterThan(0);
			expect(results[0].n).toContain('John');
		});
	});

	describe('Score operations', () => {
		it('should create and retrieve scores', async () => {
			const score: Score = {
				s: 'sch_scr',
				u: generateId(),
				t: 1,
				c: 1,
				c: 2024,
				j: 'm', // Math
				1: 85,
				2: 90,
				e: 88
			};

			const saved = await upsertPoint(COLLECTIONS.SCORES, score);
			expect(saved.j).toBe('m');
			expect(saved[1]).toBe(85);
			expect(saved.c).toBe(1);
		});

		it('should handle optional score fields', async () => {
			const score: Score = {
				s: 'sch_scr',
				u: generateId(),
				t: 2,
				c: 1,
				c: 2024,
				j: 'e' // English
				// No CA or exam scores
			};

			const saved = await upsertPoint(COLLECTIONS.SCORES, score);
			expect(saved[1]).toBeUndefined();
			expect(saved.e).toBeUndefined();
			expect(saved.c).toBe(1);
		});
	});

	describe('AccessRequest operations', () => {
		it('should create access request', async () => {
			const request: AccessRequest = {
				s: 'sch_aro',
				u: generateId(),
				n: 'Request User',
				sc: generateId(),
				role: 'student',
				r: false
			};

			const saved = await upsertPoint(
				COLLECTIONS.ACCESS_REQUESTS,
				request
			);
			expect(saved.role).toBe('student');
			expect(saved.r).toBe(false);
		});

		it('should filter non-rejected requests', async () => {
			const schoolId = generateId();

			// Create one pending and one rejected request
			await upsertPoint(COLLECTIONS.ACCESS_REQUESTS, {
				s: 'sch_aro',
				u: generateId(),
				n: 'Pending User',
				sc: schoolId,
				role: 'teacher',
				r: false
			});

			await upsertPoint(COLLECTIONS.ACCESS_REQUESTS, {
				s: 'sch_aro',
				u: generateId(),
				n: 'Rejected User',
				sc: schoolId,
				role: 'student',
				r: true
			});

			const pending = await searchByPayload<AccessRequest>(
				COLLECTIONS.ACCESS_REQUESTS,
				{
					s: 'sch_aro',
					sc: schoolId,
					r: false
				}
			);

			expect(pending.length).toBe(1);
			expect(pending[0].n).toBe('Pending User');
		});
	});

	describe('Multitenancy', () => {
		it('should properly segregate data by tenant ID', async () => {
			// Create data with different tenant IDs
			await upsertPoint(COLLECTIONS.USERS, {
				s: 'u',
				n: 'User Data',
				e: '<EMAIL>',
				g: 'g_test'
			});

			await upsertPoint(COLLECTIONS.SCHOOLS, {
				s: 'sch',
				n: 'School Data',
				c: { next: 1 }
			});

			// Search should only return data with matching tenant ID
			const users = await searchByPayload(COLLECTIONS.USERS, {
				s: 'u'
			});
			const schools = await searchByPayload(COLLECTIONS.SCHOOLS, {
				s: 'sch'
			});

			expect(users.every((u) => u.s === 'u')).toBe(true);
			expect(schools.every((s) => s.s === 'sch')).toBe(true);
		});
	});

	describe('Delete operations', () => {
		it('should delete document by ID', async () => {
			const user: User = {
				s: 'u',
				n: 'Delete Test',
				e: '<EMAIL>',
				g: 'g_delete'
			};

			const saved = await upsertPoint(COLLECTIONS.USERS, user);
			await deleteById(COLLECTIONS.USERS, saved.id!);

			const retrieved = await getById<User>(
				COLLECTIONS.USERS,
				saved.id!
			);
			expect(retrieved).toBeNull();
		});
	});
});

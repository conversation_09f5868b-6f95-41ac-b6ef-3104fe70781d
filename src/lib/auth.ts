import { searchByPayload, upsertPoint } from '$lib/db';
import { redirect } from '@sveltejs/kit';
import type { User } from './types';

// COMMENTED OUT: Auth.js specific user creation
// TODO: Adapt for custom authentication system
export async function find_or_create_user(googleUser: {
  id: string;
  email: string;
  name: string;
}): Promise<User> {
  // Check if user exists
  const existingUsers = await searchByPayload<User>({
    g: googleUser.id,
    s: 'u'
  }, 1);

  if (existingUsers.length > 0) {
    return existingUsers[0];
  }

  // Create new user
  const newUser: User = {
    s: 'u',
    n: googleUser.name,
    e: googleUser.email,
    g: googleUser.id
  };

  return await upsertPoint(newUser);
}

// PLACEHOLDER: Mock authentication requirement
// TODO: Replace with custom authentication system
export async function requireAuth(locals: App.Locals) {
    const session = await locals.auth();
    if (!session?.user) {
        console.log('PLACEHOLDER: No session found, would redirect to auth');
        // TODO: Implement proper authentication redirect
        // throw redirect(303, '/auth');

        // For now, return mock session to prevent errors
        return {
            user: {
                id: 'mock-user-id',
                name: 'Mock User',
                email: '<EMAIL>'
            }
        };
    }
    return session;
}
export interface User {
	s: 'u';
	n: string; // user's name
	e: string; // user email
	g: string; // google id
	id?: string;
}

export interface School {
	s: 'sch';
	n: string; // name
	c?: { [key: string]: string | number; n: number }; // academic classes with next
	id?: string;
}

export interface SchoolUser {
	s: 'sch_usr';
	r: string; // role
	u: string; // user uuid - references User.id
	sc: string; // school uuid
	n: string; // name
	c: number; // class key
	id?: string;
}

export interface Score {
	s: 'sch_scr';
	u: string; // school_user uuid
	t: 1 | 2 | 3; // term
	c: number; // class key (was academic year key)
	y: number; // academic session
	j: string; // subject code
	1?: number; // 1st CA
	2?: number; // 2nd CA
	3?: number; // 3rd CA
	p?: number; // project
	e?: number; // exam
	id?: string;
}

export interface AccessRequest {
	id?: string;
	u: string; // user uuid
	n: string; // name
	s: 'sch_aro';
	r?: boolean; // rejected
	role?: 'student' | 'teacher' | 'admin'; // requested role
	sc: string; // school uuid
}

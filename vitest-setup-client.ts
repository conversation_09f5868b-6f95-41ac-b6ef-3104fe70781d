import '@testing-library/jest-dom/vitest';
import { vi } from 'vitest';

// required for svelte5 + jsdom as jsdom does not support matchMedia
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	enumerable: true,
	value: vi.fn().mockImplementation((query) => ({
		matches: false,
		media: query,
		onchange: null,
		addEventListener: vi.fn(),
		removeEventListener: vi.fn(),
		dispatchEvent: vi.fn()
	}))
});

// add more mocks here if you need them

// Mock necessary environment features for tests
vi.stubGlobal('matchMedia', vi.fn((query: string) => ({
	matches: false,
	media: query,
	onchange: null,
	addListener: vi.fn(),
	removeListener: vi.fn(),
	addEventListener: vi.fn(),
	removeEventListener: vi.fn(),
	dispatchEvent: vi.fn(),
})));

// Mock requestAnimationFrame
vi.stubGlobal('requestAnimationFrame', vi.fn(cb => setTimeout(cb, 16)));

// Mock anime.js
vi.mock('animejs', () => {
	const animeMock = vi.fn(() => ({
		play: vi.fn(),
		pause: vi.fn(),
		restart: vi.fn(),
	}));
	
	animeMock.timeline = vi.fn(() => ({
		add: vi.fn().mockReturnThis(),
		play: vi.fn(),
		pause: vi.fn(),
	}));
	
	return {
		default: animeMock,
	};
});

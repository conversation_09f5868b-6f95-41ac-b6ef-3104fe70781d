import { test, expect } from '@playwright/test';

test.describe('School Management System', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('homepage displays login interface', async ({ page }) => {
    // Check for main elements
    await expect(page.locator('h1')).toContainText('School Portal');
    await expect(page.locator('text=Sign in to access your school dashboard')).toBeVisible();
    await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
  });

  test('can simulate Google login', async ({ page }) => {
    // Click the Google login button
    await page.click('button:has-text("Continue with Google")');
    
    // Should redirect to dashboard after auth
    await page.waitForURL('/d');
    await expect(page.locator('h1')).toContainText('Dashboard');
  });

  test.describe('Authenticated user flows', () => {
    test.beforeEach(async ({ page }) => {
      // Simulate login
      await page.goto('/auth/callback?id=test123&email=<EMAIL>&name=Test User');
      await page.waitForURL('/d');
    });

    test('dashboard shows create school and request access options', async ({ page }) => {
      await expect(page.locator('text=Create School')).toBeVisible();
      await expect(page.locator('text=Request Access')).toBeVisible();
    });

    test('can create a new school', async ({ page }) => {
      // Navigate to create school
      await page.click('text=Create School');
      await page.waitForURL('/sc/a');

      // Fill in school name
      await page.fill('input[name="name"]', 'Test Elementary School');
      await page.click('button:has-text("Create School")');

      // Should redirect to edit page
      await page.waitForURL(/\/sc\/.*\/edit/);
      await expect(page.locator('input[name="name"]')).toHaveValue('Test Elementary School');
    });

    test('can request access to a school', async ({ page }) => {
      // First create a school
      await page.goto('/sc/a');
      await page.fill('input[name="name"]', 'Existing School');
      await page.click('button:has-text("Create School")');
      await page.waitForURL(/\/sc\/.*\/edit/);

      // Navigate to request access
      await page.goto('/sc/ra');
      
      // Select school and role
      await page.selectOption('select[name="school"]', { index: 1 });
      await page.selectOption('select[name="role"]', 'student');
      
      // Submit request
      await page.click('button:has-text("Submit Request")');
      
      // Check for success message
      await expect(page.locator('text=Join request submitted successfully')).toBeVisible();
    });

    test('can edit school details', async ({ page }) => {
      // Create a school first
      await page.goto('/sc/a');
      await page.fill('input[name="name"]', 'School to Edit');
      await page.click('button:has-text("Create School")');
      await page.waitForURL(/\/sc\/.*\/edit/);

      // Edit school name
      await page.fill('input[name="name"]', 'Updated School Name');
      
      // Add academic year
      await page.fill('input[name="newYear"]', '2024/2025');
      await page.click('button:has-text("Add Year")');
      
      // Save changes
      await page.click('button:has-text("Save Changes")');
      
      // Should redirect to school detail page
      await page.waitForURL(/\/sc\/[^\/]+$/);
      await expect(page.locator('h1')).toContainText('Updated School Name');
    });

    test('admin can view and approve join requests', async ({ page }) => {
      // Create a school and join request
      await page.goto('/sc/a');
      await page.fill('input[name="name"]', 'Admin Test School');
      await page.click('button:has-text("Create School")');
      const schoolUrl = page.url();
      const schoolId = schoolUrl.split('/')[4];

      // Create another user's join request - simulate different user
      await page.goto(`/auth/callback?id=other_user&email=<EMAIL>&name=Other User`);
      await page.waitForURL('/d');
      await page.goto('/sc/ra');
      await page.selectOption('select[name="school"]', { index: 1 });
      await page.selectOption('select[name="role"]', 'student');
      await page.click('button:has-text("Submit Request")');

      // Login back as admin
      await page.goto('/auth/callback?id=test123&email=<EMAIL>&name=Test User');
      await page.waitForURL('/d');
      
      // Navigate to join requests
      await page.goto('/sc/r');
      
      // Should see the pending request
      await expect(page.locator('text=Other User')).toBeVisible();
      
      // Approve the request
      await page.click('button[aria-label="Grant access"]');
      
      // Request should be removed from list
      await expect(page.locator('text=Other User')).not.toBeVisible();
    });

    test('can add student scores', async ({ page }) => {
      // Create school and navigate to add score
      await page.goto('/sc/a');
      await page.fill('input[name="name"]', 'Score Test School');
      await page.click('button:has-text("Create School")');
      
      await page.goto('/s/a');
      
      // Search for student
      await page.click('button:has-text("Search for student")');
      await page.fill('input[placeholder="Search by name"]', 'Test');
      await page.keyboard.press('Enter');
      
      // Fill in score details
      await page.selectOption('select[name="term"]', '1');
      await page.selectOption('select[name="year"]', { index: 1 });
      await page.fill('input[name="session"]', '2024');
      await page.selectOption('select[name="subject"]', 'm');
      
      // Fill in scores
      await page.fill('input[name="ca1"]', '85');
      await page.fill('input[name="ca2"]', '90');
      await page.fill('input[name="exam"]', '88');
      
      // Submit
      await page.click('button:has-text("Save Score")');
      
      // Check for success
      await expect(page.locator('text=Score saved successfully')).toBeVisible();
    });

    test('can view score details', async ({ page }) => {
      // This would require creating a score first
      // For brevity, we'll test navigation
      await page.goto('/s/test-score-id');
      
      // Should show score details or redirect
      const url = page.url();
      expect(url).toMatch(/\/s\/.*/);
    });
  });

  test('unauthenticated users are redirected', async ({ page }) => {
    // Try to access protected routes
    await page.goto('/d');
    await page.waitForURL('/');
    
    await page.goto('/sc/a');
    await page.waitForURL('/');
    
    await page.goto('/sc/ra');
    await page.waitForURL('/');
  });

  test('logout functionality', async ({ page }) => {
    // Login first
    await page.goto('/auth/callback?id=test123&email=<EMAIL>&name=Test User');
    await page.waitForURL('/d');
    
    // Logout
    await page.click('button[aria-label="Logout"]');
    
    // Should redirect to homepage
    await page.waitForURL('/');
    await expect(page.locator('text=Continue with Google')).toBeVisible();
  });
}); 
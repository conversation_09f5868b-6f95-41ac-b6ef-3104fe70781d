# School Management Dashboard

A modern, beautiful school management system built with SvelteKit, Qdrant vector database, and Tailwind CSS v4.

## Features

✅ **Authentication**
- Google OAuth login (simulated for development)
- Session-based authentication
- Protected routes

✅ **School Management**
- Create and edit schools
- Manage academic years
- Admin role assignment

✅ **Access Control**
- Request access to schools (student/teacher/admin roles)
- Admin approval workflow
- Role-based permissions

✅ **Score Management**
- Add student scores by term and subject
- Track continuous assessments and exams
- View detailed score records

## Tech Stack

- **Frontend**: SvelteKit with Svelte 5
- **Database**: Qdrant vector database (multitenancy with `s` field)
- **Styling**: Tailwind CSS v4 with glassmorphic design
- **Animations**: Anime.js
- **IDs**: UUID v7 for unique identifiers
- **Testing**: Vitest (unit) & Playwright (e2e)

## Prerequisites

- Node.js 18+
- Docker (for Qdrant)
- npm or pnpm

## Setup

1. **Clone and install dependencies**
```bash
cd school
npm install
```

2. **Start Qdrant database**
```bash
# Using Docker
docker run -d --name qdrant -p 6333:6333 -p 6334:6334 -v ./qdrant_storage:/qdrant/storage qdrant/qdrant:latest

# Or using npm script
npm run qdrant:start
```

3. **Set up environment variables**
```bash
# Create .env file (optional - defaults are set)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=
```

4. **Run development server**
```bash
npm run dev
```

The app will be available at http://localhost:1440

## Testing

### Unit Tests
```bash
# Run once
npm run test:unit -- --run

# Watch mode
npm run test:unit:watch
```

### End-to-End Tests
```bash
# Install Playwright browsers (first time only)
npx playwright install chromium

# Run e2e tests
npm run test:e2e

# Run all tests (starts Qdrant automatically)
npm run test:all
```

## Project Structure

```
school/
├── src/
│   ├── routes/          # SvelteKit routes
│   │   ├── +page.svelte # Login page
│   │   ├── d/           # Dashboard
│   │   ├── sc/          # School routes
│   │   │   ├── c/       # Create school
│   │   │   ├── [id]/    # School detail/edit
│   │   │   ├── r/       # Join requests
│   │   │   └── ra/      # Request access
│   │   └── s/           # Score routes
│   │       ├── a/       # Add score
│   │       └── [id]/    # Score detail
│   ├── lib/
│   │   ├── db.ts        # Qdrant database operations
│   │   ├── auth.server.ts # Authentication helpers
│   │   └── components/  # Shared components
│   └── app.css          # Global styles with Tailwind v4
├── e2e/                 # End-to-end tests
├── docker-compose.yml   # Qdrant setup
└── vite.config.ts       # Vite configuration
```

## Database Schema

All data uses multitenancy with the `s` field:

- **Users** (`s: 'u'`): Google authenticated users
- **Schools** (`s: 'sch'`): School records with academic years
- **School Users** (`s: 'sch_usr'`): User-school relationships with roles
- **Scores** (`s: 'sch_scr'`): Student academic scores
- **Join Requests** (`s: 'sch_aro'`): Pending join requests

## Development Notes

- Form submissions use SvelteKit form actions (server-side)
- All IDs are UUID v7 for temporal ordering
- Tailwind v4 configuration in `app.css` using `@theme`
- Beautiful glassmorphic UI with animations
- Comprehensive test coverage (unit + e2e)

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run all tests
- `npm run qdrant:start` - Start Qdrant database
- `npm run qdrant:stop` - Stop Qdrant database
- `npm run qdrant:logs` - View Qdrant logs
